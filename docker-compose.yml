services:
  kimovil-scraper:
    build: .
    env_file:
      - .env
    depends_on:
      rmq:
        condition: service_healthy
    extra_hosts:
        - "host.docker.internal:host-gateway"
    restart: on-failure
  rmq:
    build:
      context: .
      dockerfile: Dockerfile.rmq
    env_file:
      - .env
    ports:
      - "5679:5672"
      - "15679:15672"
    volumes:
      - kimovil-rabbitmq-data:/var/lib/rabbitmq
    healthcheck:
        test: ["CMD", "curl", "-f", "http://localhost:15672"]
        interval: 5s
        timeout: 5s
        retries: 5

volumes:
  kimovil-rabbitmq-data:
