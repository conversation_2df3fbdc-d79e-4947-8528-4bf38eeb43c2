name = "kimovil-scraper-worker"
main = "src/index.ts"
compatibility_date = "2024-12-18"
compatibility_flags = ["nodejs_compat"]

[env.production]
name = "kimovil-scraper-worker"

[env.staging]
name = "kimovil-scraper-worker-staging"

# 环境变量配置
[vars]
ENVIRONMENT = "production"

# 绑定配置（如果需要KV存储或其他服务）
# [[kv_namespaces]]
# binding = "CACHE"
# id = "your-kv-namespace-id"
# preview_id = "your-preview-kv-namespace-id"
