# Kimovil 手机数据爬虫 - Cloudflare Workers 版本

一个基于 Cloudflare Workers 的高性能手机数据爬虫，用于从 Kimovil 网站抓取手机规格和价格信息。

## ✨ 特性

- 🚀 **边缘计算**: 基于 Cloudflare Workers，全球低延迟访问
- 🤖 **AI 增强**: 集成 OpenAI GPT-4o-mini 优化数据质量
- 📱 **完整数据**: 抓取手机的详细规格、价格、图片等信息
- 🔍 **智能搜索**: 自动完成和智能匹配功能
- 🌐 **RESTful API**: 简洁的 HTTP API 接口
- ⚡ **高性能**: 无服务器架构，按需扩展

## 🏗️ 架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   用户请求      │───▶│ Cloudflare       │───▶│   Kimovil       │
│                 │    │ Workers          │    │   网站          │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   OpenAI API     │
                       │   数据优化       │
                       └──────────────────┘
```

## 📋 API 接口

### 1. 获取自动完成选项
```http
GET /api/autocomplete?q={搜索关键词}
```

**示例:**
```bash
curl "https://your-worker.your-subdomain.workers.dev/api/autocomplete?q=iPhone%2014"
```

**响应:**
```json
[
  {
    "name": "Apple iPhone 14",
    "slug": "apple-iphone-14"
  },
  {
    "name": "Apple iPhone 14 Pro",
    "slug": "apple-iphone-14-pro"
  }
]
```

### 2. 匹配最佳 Slug
```http
POST /api/match-slug
Content-Type: application/json

{
  "searchString": "iPhone 14",
  "options": [...]
}
```

**响应:**
```json
{
  "slug": "apple-iphone-14"
}
```

### 3. 抓取手机数据
```http
GET /api/scrape?slug={手机slug}
```

**示例:**
```bash
curl "https://your-worker.your-subdomain.workers.dev/api/scrape?slug=apple-iphone-14"
```

**响应:**
```json
{
  "slug": "apple-iphone-14",
  "name": "Apple iPhone 14",
  "brand": "Apple",
  "releaseDate": "2022-09-16T00:00:00.000Z",
  "height_mm": 146.7,
  "width_mm": 71.5,
  "thickness_mm": 7.8,
  "weight_g": 172,
  "size_in": 6.1,
  "displayType": "Super Retina XDR OLED",
  "resolution": "2556 x 1179",
  "cpu": "Apple A15 Bionic",
  "cpuManufacturer": "Apple",
  "batteryCapacity_mah": 3279,
  "cameras": [...],
  "os": "iOS 16",
  ...
}
```

## 🚀 快速开始

### 前置要求

- Node.js 18+
- npm 或 yarn
- Cloudflare 账户
- OpenAI API Key

### 1. 克隆项目

```bash
git clone https://github.com/your-username/kimovil-scraper-worker.git
cd kimovil-scraper-worker
```

### 2. 安装依赖

```bash
npm install
```

### 3. 配置环境变量

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
OPENAI_API_KEY=your_openai_api_key_here
ENVIRONMENT=development
```

### 4. 本地开发

```bash
npm run dev
```

访问 `http://localhost:8787` 查看 API 文档。

### 5. 部署到 Cloudflare Workers

首先登录 Cloudflare：
```bash
npx wrangler login
```

配置 `wrangler.toml` 中的项目名称，然后部署：
```bash
npm run deploy
```

## ⚙️ 配置

### wrangler.toml

```toml
name = "kimovil-scraper-worker"
main = "src/index.ts"
compatibility_date = "2024-12-18"
compatibility_flags = ["nodejs_compat"]

[env.production]
name = "kimovil-scraper-worker"

[env.staging]
name = "kimovil-scraper-worker-staging"

[vars]
ENVIRONMENT = "production"
```

### 环境变量

在 Cloudflare Workers 控制台中设置以下环境变量：

- `OPENAI_API_KEY`: OpenAI API 密钥
- `ENVIRONMENT`: 环境标识（development/production）

## 📊 数据结构

### PhoneData 接口

```typescript
interface PhoneData {
  // 基本信息
  slug: string;
  name: string;
  brand: string;
  aliases: string;
  releaseDate: Date | null;
  
  // 设计
  height_mm: number | null;
  width_mm: number | null;
  thickness_mm: number | null;
  weight_g: number | null;
  materials: string;
  ipRating: string | null;
  colors: string;
  
  // 显示屏
  size_in: number | null;
  displayType: string | null;
  resolution: string | null;
  aspectRatio: string | null;
  ppi: number | null;
  displayFeatures: string;
  
  // 硬件
  cpu: string | null;
  cpuManufacturer: string | null;
  cpuCores: string | null;
  gpu: string | null;
  skus: Sku[];
  benchmarks: Benchmark[];
  
  // 连接性
  nfc: boolean | null;
  bluetooth: string | null;
  sim: string;
  simCount: number;
  usb: "USB-C" | "Lightning" | null;
  headphoneJack: boolean | null;
  
  // 电池
  batteryCapacity_mah: number | null;
  batteryFastCharging: boolean | null;
  batteryWattage: number | null;
  
  // 相机
  cameras: SingleCameraData[];
  cameraFeatures: string;
  
  // 软件
  os: string | null;
  osSkin: string | null;
}
```

## 🔧 开发

### 项目结构

```
src/
├── index.ts              # 主入口文件，HTTP API 路由
├── modules/
│   ├── ai/
│   │   └── openai.ts     # OpenAI 集成
│   └── scraping/
│       └── fetch.ts      # 网页抓取模块
├── types/
│   ├── index.ts          # 类型定义
│   └── payloads.ts       # 请求/响应类型
└── utils/
    ├── consts.ts         # 常量定义
    └── logging.ts        # 日志工具
```

### 添加新功能

1. 在相应模块中添加功能
2. 更新类型定义
3. 在 `src/index.ts` 中添加新的 API 路由
4. 更新文档

### 测试

```bash
# 本地测试
npm run dev

# 测试自动完成
curl "http://localhost:8787/api/autocomplete?q=iPhone"

# 测试数据抓取
curl "http://localhost:8787/api/scrape?slug=apple-iphone-14"
```

## 📈 性能优化

- **缓存策略**: 可以集成 Cloudflare KV 存储来缓存抓取结果
- **并发控制**: Workers 自动处理并发请求
- **错误处理**: 完善的错误处理和重试机制
- **数据压缩**: 自动 gzip 压缩响应

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 ISC 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Kimovil](https://www.kimovil.com) - 数据源
- [OpenAI](https://openai.com) - AI 数据处理
- [Cloudflare Workers](https://workers.cloudflare.com) - 边缘计算平台

## 📞 支持

如有问题，请：

1. 查看 [Issues](https://github.com/your-username/kimovil-scraper-worker/issues)
2. 创建新的 Issue
3. 联系维护者

---

**注意**: 请遵守 Kimovil 网站的使用条款和 robots.txt 规则，合理使用本工具。
