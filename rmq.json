{"rabbit_version": "3.12.10", "rabbitmq_version": "3.12.10", "product_name": "RabbitMQ", "product_version": "3.12.10", "users": [{"name": "admin", "password_hash": "PxzCix7RmqIqaP87PFqFeLNdbwTfWYfpZOk8DdCQtW+FjBbb", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": ["administrator"], "limits": {}}], "vhosts": [{"name": "/"}], "permissions": [{"user": "admin", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"}], "topic_permissions": [], "parameters": [], "global_parameters": [{"name": "internal_cluster_id", "value": "rabbitmq-cluster-id-Zj4xTK1g-qNe5uIocCEXGw"}], "policies": [], "queues": [{"name": "errorResponse", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "getMatchingSlugRequest", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "getKimovilDataResponse", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "getKimovilDataRequest", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "getAutocompleteOptionsRequest", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "getUserConfirmedSlugRequest", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}], "exchanges": [], "bindings": []}