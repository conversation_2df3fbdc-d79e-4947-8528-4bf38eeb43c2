export const debugLog = (...args: unknown[]) => {
  // 在 Cloudflare Workers 中，使用环境变量或全局变量检查调试模式
  const isDevelopment = typeof globalThis !== 'undefined' &&
    (globalThis as any).ENVIRONMENT === "development";

  if (isDevelopment) {
    console.log("[DEBUG]", ...args);
  }
};

export const errorLog = (...args: unknown[]) => {
  console.error("[ERROR]", ...args);
};

export const withDebugLog = <Args extends unknown[], R>(
  fn: (...args: Args) => Promise<R>,
  tag?: string
): ((...args: Args) => Promise<R>) => {
  return async (...args: Args) => {
    const result = await fn(...args);
    const logArguments: Array<string | R> = tag ? [tag] : [];
    logArguments.push(result);
    debugLog(...logArguments);
    return result;
  };
};
