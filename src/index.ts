import {
  getAutocompleteOptions,
  scrapeBySlug,
} from "./modules/scraping/fetch.js";
import { pickMatchingSlug } from "./modules/ai/openai.js";

// Cloudflare Workers 环境接口
interface Env {
  OPENAI_API_KEY: string;
  ENVIRONMENT?: string;
}

// 错误响应辅助函数
function errorResponse(message: string, status: number = 400) {
  return new Response(JSON.stringify({ error: message }), {
    status,
    headers: { 'Content-Type': 'application/json' },
  });
}

// 成功响应辅助函数（暂时未使用，保留备用）
// function successResponse(data: any) {
//   return new Response(JSON.stringify(data), {
//     headers: { 'Content-Type': 'application/json' },
//   });
// }

// 主要的 fetch 处理函数
export default {
  async fetch(request: Request, env: Env, _ctx: ExecutionContext): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    // 设置 CORS 头
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    };

    // 处理 OPTIONS 请求（CORS 预检）
    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }

    try {
      // 路由处理
      if (path === '/api/autocomplete' && request.method === 'GET') {
        const searchString = url.searchParams.get('q');
        if (!searchString) {
          return errorResponse('Missing search query parameter "q"');
        }

        const options = await getAutocompleteOptions(searchString);
        return new Response(JSON.stringify(options), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      if (path === '/api/match-slug' && request.method === 'POST') {
        const body = await request.json() as { searchString: string; options: any[] };
        if (!body.searchString || !body.options) {
          return errorResponse('Missing searchString or options in request body');
        }

        const slug = await pickMatchingSlug(body.searchString, body.options, env);
        return new Response(JSON.stringify({ slug }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      if (path === '/api/scrape' && request.method === 'GET') {
        const slug = url.searchParams.get('slug');
        if (!slug) {
          return errorResponse('Missing slug parameter');
        }

        const data = await scrapeBySlug(slug, env);
        return new Response(JSON.stringify(data), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // 根路径 - 返回 API 文档
      if (path === '/' && request.method === 'GET') {
        const apiDocs = {
          name: 'Kimovil Scraper API',
          version: '1.0.0',
          description: 'Kimovil手机数据爬虫API - Cloudflare Workers版本',
          endpoints: {
            'GET /api/autocomplete?q={searchString}': '获取自动完成选项',
            'POST /api/match-slug': '匹配最佳slug（需要body: {searchString, options}）',
            'GET /api/scrape?slug={slug}': '抓取手机数据',
          },
          examples: {
            autocomplete: '/api/autocomplete?q=iPhone%2014',
            scrape: '/api/scrape?slug=apple-iphone-14',
          }
        };

        return new Response(JSON.stringify(apiDocs, null, 2), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // 404 处理
      return errorResponse('Not Found', 404);

    } catch (error) {
      console.error('Error processing request:', error);
      return errorResponse('Internal Server Error', 500);
    }
  },
};
