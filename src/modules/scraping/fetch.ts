import {
  AutocompleteOption,
  PhoneData,
} from "../../types/index.js";
import { debugLog, withDebugLog } from "../../utils/logging.js";
import { adaptScrapedData } from "../ai/openai.js";

// 简单的HTML解析器类
class SimpleHTMLParser {
  private html: string;

  constructor(html: string) {
    this.html = html;
  }

  // 提取文本内容
  extractText(selector: string): string | null {
    try {
      // 简化的选择器解析，主要处理基本的CSS选择器
      const patterns = this.createSelectorPatterns(selector);
      
      for (const pattern of patterns) {
        const match = this.html.match(pattern);
        if (match) {
          const content = match[1] || match[0];
          // 移除HTML标签并清理文本
          return this.cleanText(content);
        }
      }
      return null;
    } catch (error) {
      debugLog(`Error extracting text for selector ${selector}:`, error);
      return null;
    }
  }

  // 提取属性值
  extractAttribute(selector: string, attribute: string): string | null {
    try {
      const patterns = this.createSelectorPatterns(selector);
      
      for (const pattern of patterns) {
        const match = this.html.match(pattern);
        if (match) {
          const element = match[0];
          const attrPattern = new RegExp(`${attribute}=["']([^"']*?)["']`, 'i');
          const attrMatch = element.match(attrPattern);
          return attrMatch ? attrMatch[1] : null;
        }
      }
      return null;
    } catch (error) {
      debugLog(`Error extracting attribute ${attribute} for selector ${selector}:`, error);
      return null;
    }
  }

  // 提取多个匹配项
  extractAll(selector: string): string[] {
    try {
      const patterns = this.createSelectorPatterns(selector);
      const results: string[] = [];
      
      for (const pattern of patterns) {
        const matches = this.html.matchAll(new RegExp(pattern.source, 'gi'));
        for (const match of matches) {
          const content = match[1] || match[0];
          results.push(this.cleanText(content));
        }
      }
      return results;
    } catch (error) {
      debugLog(`Error extracting all for selector ${selector}:`, error);
      return [];
    }
  }

  private createSelectorPatterns(selector: string): RegExp[] {
    const patterns: RegExp[] = [];
    
    // 处理基本的标签选择器
    if (selector.includes('h3:has-text')) {
      const textMatch = selector.match(/h3:has-text\("([^"]+)"\)/);
      if (textMatch) {
        patterns.push(new RegExp(`<h3[^>]*>[^<]*${textMatch[1]}[^<]*</h3>`, 'gi'));
      }
    }
    
    // 处理类选择器
    if (selector.includes('.')) {
      const className = selector.replace(/.*\.([a-zA-Z0-9_-]+).*/, '$1');
      patterns.push(new RegExp(`<[^>]*class=["'][^"']*${className}[^"']*["'][^>]*>([^<]*)</[^>]*>`, 'gi'));
    }
    
    // 处理ID选择器
    if (selector.includes('#')) {
      const id = selector.replace(/.*#([a-zA-Z0-9_-]+).*/, '$1');
      patterns.push(new RegExp(`<[^>]*id=["']${id}["'][^>]*>([^<]*)</[^>]*>`, 'gi'));
    }
    
    // 处理属性选择器
    if (selector.includes('[') && selector.includes(']')) {
      const attrMatch = selector.match(/\[([^=\]]+)(?:=["']?([^"'\]]+)["']?)?\]/);
      if (attrMatch) {
        const attr = attrMatch[1];
        const value = attrMatch[2];
        if (value) {
          patterns.push(new RegExp(`<[^>]*${attr}=["'][^"']*${value}[^"']*["'][^>]*>([^<]*)</[^>]*>`, 'gi'));
        } else {
          patterns.push(new RegExp(`<[^>]*${attr}[^>]*>([^<]*)</[^>]*>`, 'gi'));
        }
      }
    }
    
    // 基本标签选择器
    const tagMatch = selector.match(/^([a-zA-Z0-9]+)/);
    if (tagMatch && patterns.length === 0) {
      patterns.push(new RegExp(`<${tagMatch[1]}[^>]*>([^<]*)</${tagMatch[1]}>`, 'gi'));
    }
    
    return patterns;
  }

  private cleanText(text: string): string {
    return text
      .replace(/<[^>]*>/g, '') // 移除HTML标签
      .replace(/&nbsp;/g, ' ') // 替换&nbsp;
      .replace(/&amp;/g, '&') // 替换&amp;
      .replace(/&lt;/g, '<') // 替换&lt;
      .replace(/&gt;/g, '>') // 替换&gt;
      .replace(/&quot;/g, '"') // 替换&quot;
      .replace(/&#39;/g, "'") // 替换&#39;
      .replace(/\s+/g, ' ') // 合并多个空格
      .trim();
  }
}

export const scrapeBySlug = withDebugLog(async (slug: string, env?: any): Promise<PhoneData> => {
  try {
    const url = `https://www.kimovil.com/en/where-to-buy-${slug}`;
    
    debugLog(`Fetching ${url}`);
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const html = await response.text();
    const parser = new SimpleHTMLParser(html);

    debugLog(`Successfully fetched page content, length: ${html.length}`);

    // 提取基本信息
    const name = parser.extractText('h1.device-name') || 
                 parser.extractText('h1') || 
                 parser.extractText('.device-title') || 
                 'Unknown Device';
    
    const brand = name.split(' ')[0] || 'Unknown';

    // 提取发布日期
    const releaseDateText = parser.extractText('.release-date') || 
                           parser.extractText('[data-release-date]');
    let releaseDate: Date | null = null;
    if (releaseDateText) {
      const parsedDate = new Date(releaseDateText);
      if (!isNaN(parsedDate.getTime())) {
        releaseDate = parsedDate;
      }
    }

    // 提取尺寸信息
    const dimensionsText = parser.extractText('.dimensions') || 
                          parser.extractText('[data-dimensions]');
    let height_mm: number | null = null;
    let width_mm: number | null = null;
    let thickness_mm: number | null = null;
    
    if (dimensionsText) {
      const dimMatch = dimensionsText.match(/(\d+\.?\d*)\s*x\s*(\d+\.?\d*)\s*x\s*(\d+\.?\d*)/);
      if (dimMatch) {
        height_mm = parseFloat(dimMatch[1]);
        width_mm = parseFloat(dimMatch[2]);
        thickness_mm = parseFloat(dimMatch[3]);
      }
    }

    // 提取重量
    const weightText = parser.extractText('.weight') || 
                      parser.extractText('[data-weight]');
    let weight_g: number | null = null;
    if (weightText) {
      const weightMatch = weightText.match(/(\d+\.?\d*)\s*g/);
      if (weightMatch) {
        weight_g = parseFloat(weightMatch[1]);
      }
    }

    // 提取显示屏信息
    const displaySizeText = parser.extractText('.display-size') || 
                           parser.extractText('[data-display-size]');
    let size_in: number | null = null;
    if (displaySizeText) {
      const sizeMatch = displaySizeText.match(/(\d+\.?\d*)\s*["'']/);
      if (sizeMatch) {
        size_in = parseFloat(sizeMatch[1]);
      }
    }

    const displayType = parser.extractText('.display-type') || 
                       parser.extractText('[data-display-type]');
    
    const resolution = parser.extractText('.resolution') || 
                      parser.extractText('[data-resolution]');

    // 提取处理器信息
    const cpuText = parser.extractText('.processor') || 
                   parser.extractText('.cpu') || 
                   parser.extractText('[data-processor]');
    
    let cpu: string | null = null;
    let cpuManufacturer: string | null = null;
    
    if (cpuText) {
      const manufacturers = ['Snapdragon', 'MediaTek', 'Exynos', 'Apple', 'Kirin', 'Unisoc'];
      for (const manufacturer of manufacturers) {
        if (cpuText.includes(manufacturer)) {
          cpuManufacturer = manufacturer;
          cpu = cpuText.replace(/\([^)]*\)/g, '').trim();
          break;
        }
      }
    }

    // 提取电池信息
    const batteryText = parser.extractText('.battery') || 
                       parser.extractText('[data-battery]');
    let batteryCapacity_mah: number | null = null;
    if (batteryText) {
      const batteryMatch = batteryText.match(/(\d+)\s*mAh/);
      if (batteryMatch) {
        batteryCapacity_mah = parseInt(batteryMatch[1]);
      }
    }

    // 构建基础数据对象
    const data: PhoneData = {
      slug,
      name,
      brand,
      aliases: '',
      releaseDate,
      raw: html.slice(
        Math.max(0, html.indexOf('<main')),
        html.indexOf('</main>') > 0 ? html.indexOf('</main>') + 7 : html.length
      ),
      
      // 设计
      height_mm,
      width_mm,
      thickness_mm,
      weight_g,
      materials: '',
      ipRating: null,
      colors: '',
      
      // 显示屏
      size_in,
      displayType,
      resolution,
      aspectRatio: null,
      ppi: null,
      displayFeatures: '',
      
      // 硬件
      cpu,
      cpuManufacturer,
      cpuCores: null,
      gpu: null,
      sdSlot: null,
      skus: [],
      fingerprintPosition: null,
      benchmarks: [],
      
      // 连接性
      nfc: null,
      bluetooth: null,
      sim: '',
      simCount: 1,
      usb: null,
      headphoneJack: null,
      
      // 电池
      batteryCapacity_mah,
      batteryFastCharging: null,
      batteryWattage: null,
      
      // 相机
      cameras: [],
      cameraFeatures: '',
      
      // 软件
      os: null,
      osSkin: null,
    };

    debugLog(`Extracted basic data for ${name}`);

    // 尝试使用AI优化数据
    try {
      debugLog(`Trying to adapt data with OpenAI gpt-4o-mini.`);
      const adaptedData = await adaptScrapedData(data, env);
      debugLog(`Successfully adapted data:`, JSON.stringify(adaptedData));
      if (adaptedData) {
        adaptedData.raw = data.raw;
        return adaptedData;
      }
    } catch (aiError) {
      debugLog(`AI adaptation failed, using raw data:`, aiError);
    }

    return data;
  } catch (error) {
    debugLog(`Error scraping ${slug}:`, error);
    throw error;
  }
});

export const getAutocompleteOptions = withDebugLog(
  async (name: string): Promise<AutocompleteOption[]> => {
    try {
      const url = `https://www.kimovil.com/_json/autocomplete_devicemodels_joined.json?device_type=0&name=${encodeURIComponent(name)}`;
      
      debugLog(`Fetching autocomplete options from ${url}`);
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const jsonResponse = await response.json() as {
        results: { full_name: string; url: string }[];
      };

      return jsonResponse.results.map(({ full_name: name, url: slug }) => ({
        name,
        slug,
      }));
    } catch (error) {
      debugLog(`Error getting autocomplete options for ${name}:`, error);
      throw error;
    }
  }
);
